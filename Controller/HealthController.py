from fastapi import APIRouter
from Models.AjaxResult import AjaxResult
from Utils.AsyncConfig import async_config
from Utils.DeadlockMonitor import deadlock_monitor
from Utils.logs.LoggingConfig import logger
import time
import threading
import asyncio

router = APIRouter(prefix="/health", tags=["健康检查"])


@router.get("/status")
async def get_health_status():
    """获取系统健康状态"""
    try:
        status = {
            "timestamp": time.time(),
            "status": "healthy",
            "components": {}
        }
        
        # 检查线程池状态
        thread_pool_status = _check_thread_pools()
        status["components"]["thread_pools"] = thread_pool_status
        
        # 检查死锁监控状态
        deadlock_status = _check_deadlock_monitor()
        status["components"]["deadlock_monitor"] = deadlock_status
        
        # 检查事件循环状态
        event_loop_status = _check_event_loop()
        status["components"]["event_loop"] = event_loop_status
        
        # 判断整体健康状态
        all_healthy = all(
            comp.get("status") == "healthy" 
            for comp in status["components"].values()
        )
        
        if not all_healthy:
            status["status"] = "degraded"
        
        return AjaxResult.success(status)
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return AjaxResult.error(f"Health check failed: {str(e)}")


@router.get("/deadlock-report")
async def get_deadlock_report():
    """获取死锁监控报告"""
    try:
        report = deadlock_monitor.get_status_report()
        return AjaxResult.success(report)
    except Exception as e:
        logger.error(f"Failed to get deadlock report: {str(e)}")
        return AjaxResult.error(f"Failed to get deadlock report: {str(e)}")


@router.get("/thread-pools")
async def get_thread_pool_status():
    """获取线程池详细状态"""
    try:
        status = _check_thread_pools()
        return AjaxResult.success(status)
    except Exception as e:
        logger.error(f"Failed to get thread pool status: {str(e)}")
        return AjaxResult.error(f"Failed to get thread pool status: {str(e)}")


def _check_thread_pools():
    """检查线程池状态"""
    status = {
        "status": "healthy",
        "pools": {}
    }
    
    try:
        executors = [
            ("llm", async_config._llm_executor, async_config.max_llm_workers),
            ("file", async_config._file_executor, async_config.max_file_workers),
            ("download", async_config._download_executor, async_config.max_download_workers)
        ]
        
        for name, executor, max_workers in executors:
            if executor is None:
                pool_status = {
                    "status": "not_initialized",
                    "max_workers": max_workers,
                    "active_threads": 0,
                    "queue_size": 0
                }
            elif executor._shutdown:
                pool_status = {
                    "status": "shutdown",
                    "max_workers": max_workers,
                    "active_threads": 0,
                    "queue_size": 0
                }
            else:
                active_threads = len(getattr(executor, '_threads', set()))
                queue_size = executor._work_queue.qsize() if hasattr(executor, '_work_queue') else 0
                
                pool_status = {
                    "status": "healthy",
                    "max_workers": max_workers,
                    "active_threads": active_threads,
                    "queue_size": queue_size
                }
                
                # 检查是否有问题
                if queue_size > max_workers * 2:
                    pool_status["status"] = "overloaded"
                    status["status"] = "degraded"
                elif active_threads == max_workers and queue_size > 0:
                    pool_status["status"] = "busy"
            
            status["pools"][name] = pool_status
            
    except Exception as e:
        logger.error(f"Error checking thread pools: {str(e)}")
        status["status"] = "error"
        status["error"] = str(e)
    
    return status


def _check_deadlock_monitor():
    """检查死锁监控状态"""
    try:
        report = deadlock_monitor.get_status_report()
        
        status = {
            "status": "healthy",
            "is_monitoring": deadlock_monitor.is_monitoring,
            "total_tasks": report["total_tasks"],
            "long_running_tasks": report["long_running_tasks"]
        }
        
        if report["long_running_tasks"] > 0:
            status["status"] = "warning"
            status["warning"] = f"Found {report['long_running_tasks']} long running tasks"
        
        if not deadlock_monitor.is_monitoring:
            status["status"] = "disabled"
        
        return status
        
    except Exception as e:
        logger.error(f"Error checking deadlock monitor: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }


def _check_event_loop():
    """检查事件循环状态"""
    try:
        loop = asyncio.get_running_loop()
        
        status = {
            "status": "healthy",
            "is_running": loop.is_running(),
            "is_closed": loop.is_closed()
        }
        
        if loop.is_closed():
            status["status"] = "error"
            status["error"] = "Event loop is closed"
        elif not loop.is_running():
            status["status"] = "warning"
            status["warning"] = "Event loop is not running"
        
        return status
        
    except RuntimeError as e:
        return {
            "status": "error",
            "error": f"No running event loop: {str(e)}"
        }
    except Exception as e:
        logger.error(f"Error checking event loop: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }


@router.post("/force-cleanup")
async def force_cleanup():
    """强制清理资源（紧急情况使用）"""
    try:
        logger.warning("Force cleanup requested")
        
        # 清理长时间运行的任务
        long_running = deadlock_monitor.get_long_running_tasks()
        if long_running:
            logger.warning(f"Found {len(long_running)} long running tasks during force cleanup")
            for task_info in long_running:
                deadlock_monitor.unregister_task(task_info.task_id)
        
        # 重启线程池（如果需要）
        cleanup_results = {
            "long_running_tasks_cleared": len(long_running),
            "thread_pools_restarted": False
        }
        
        return AjaxResult.success(cleanup_results)
        
    except Exception as e:
        logger.error(f"Force cleanup failed: {str(e)}")
        return AjaxResult.error(f"Force cleanup failed: {str(e)}")
