# 项目死锁预防和监控指南

## 🚨 已修复的潜在死锁问题

### 1. AsyncConfig 信号量初始化问题
**问题**: 信号量在 `__init__` 中创建，可能在错误的事件循环中使用
**修复**: 
- 延迟创建信号量，在需要时检查当前事件循环
- 添加事件循环变化检测
- 增加超时机制防止无限等待

### 2. 嵌套线程创建问题
**问题**: 在线程池中又创建新线程，可能导致资源竞争
**修复**: 
- 移除 `AsyncTalentAgent` 中的 `threading.Thread` 创建
- 改为同步执行保存操作，避免嵌套线程

### 3. LLM 调用超时问题
**问题**: LLM 调用没有超时机制，可能无限等待
**修复**: 
- 为所有线程池操作添加超时控制
- LLM 操作使用更长的超时时间（10分钟）
- 其他操作使用标准超时时间（5分钟）

### 4. 线程池状态检查
**问题**: 线程池可能在关闭后仍被使用
**修复**: 
- 添加线程池状态检查
- 自动重新创建已关闭的线程池
- 优雅关闭机制

## 🔧 新增功能

### 1. 死锁监控器 (DeadlockMonitor)
- 实时监控长时间运行的任务
- 自动检测潜在的死锁情况
- 提供详细的任务执行报告

### 2. 健康检查端点
- `/health/status` - 系统整体健康状态
- `/health/deadlock-report` - 死锁监控报告
- `/health/thread-pools` - 线程池状态详情
- `/health/force-cleanup` - 紧急清理资源

### 3. 任务监控上下文
- `TaskMonitorContext` - 自动监控任务执行时间
- 装饰器支持异步和同步函数
- 可配置的超时阈值

## 📊 配置说明

### async_config 新增配置项
```json
{
  "async_config": {
    "max_llm_workers": 5,
    "max_file_workers": 3,
    "max_download_workers": 5,
    "default_timeout": 300,    // 默认超时时间（秒）
    "llm_timeout": 600         // LLM操作超时时间（秒）
  }
}
```

## 🚀 使用方法

### 1. 监控长时间运行的任务
```python
from Utils.DeadlockMonitor import TaskMonitorContext

# 使用上下文管理器
with TaskMonitorContext("my_task", timeout_threshold=120.0):
    # 执行可能耗时的操作
    result = some_long_running_function()

# 使用装饰器
@monitor_sync_task("llm_processing", timeout_threshold=300.0)
def process_with_llm(content):
    return llm.invoke(content)
```

### 2. 检查系统健康状态
```bash
# 获取整体健康状态
curl http://localhost:8000/health/status

# 获取死锁监控报告
curl http://localhost:8000/health/deadlock-report

# 获取线程池状态
curl http://localhost:8000/health/thread-pools
```

### 3. 紧急情况处理
```bash
# 强制清理资源
curl -X POST http://localhost:8000/health/force-cleanup
```

## ⚠️ 注意事项

### 1. 避免的模式
- ❌ 在线程池中创建新线程
- ❌ 在同步代码中调用 `asyncio.run()`
- ❌ 长时间阻塞操作不设置超时
- ❌ 在不同事件循环中共享异步对象

### 2. 推荐的模式
- ✅ 使用 AsyncConfig 统一管理线程池
- ✅ 为所有耗时操作设置合理超时
- ✅ 使用监控上下文跟踪任务执行
- ✅ 定期检查健康状态端点

### 3. 监控指标
- 线程池队列长度 > max_workers * 2 时告警
- 任务执行时间超过阈值时告警
- 长时间运行任务数量 > 0 时告警

## 🔍 故障排查

### 1. 系统卡死时的检查步骤
1. 访问 `/health/status` 检查整体状态
2. 访问 `/health/deadlock-report` 查看长时间运行的任务
3. 访问 `/health/thread-pools` 检查线程池状态
4. 查看日志中的超时和错误信息

### 2. 常见问题和解决方案

#### 问题: LLM 调用超时
```
解决方案:
1. 检查 LLM 服务是否正常
2. 调整 llm_timeout 配置
3. 检查网络连接
```

#### 问题: 线程池队列积压
```
解决方案:
1. 增加对应线程池的 max_workers
2. 优化任务执行效率
3. 考虑任务分批处理
```

#### 问题: 长时间运行任务过多
```
解决方案:
1. 检查任务是否真的卡死
2. 使用 force-cleanup 清理僵尸任务
3. 重启服务释放资源
```

## 📈 性能优化建议

### 1. 线程池配置
- LLM 线程池: 根据 LLM 服务并发能力调整
- 文件处理线程池: 根据 I/O 性能调整
- 下载线程池: 根据网络带宽调整

### 2. 超时配置
- LLM 操作: 600秒（可根据模型大小调整）
- 文件操作: 300秒（可根据文件大小调整）
- 下载操作: 300秒（可根据网络情况调整）

### 3. 监控频率
- 死锁检查: 30秒间隔（默认）
- 健康检查: 按需调用
- 日志级别: 生产环境使用 INFO，调试时使用 DEBUG

## 🔄 升级和维护

### 1. 定期检查
- 每日检查健康状态端点
- 每周分析长时间运行任务趋势
- 每月评估线程池配置是否合理

### 2. 版本升级
- 升级前备份配置文件
- 测试环境验证新功能
- 生产环境灰度发布

### 3. 日志管理
- 定期清理过期日志
- 监控日志文件大小
- 设置日志轮转策略
