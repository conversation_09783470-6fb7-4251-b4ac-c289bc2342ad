<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>死锁监控详情</title>
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <meta name="description" content="实时监控长时间运行的任务和潜在的死锁情况">
    <meta name="keywords" content="死锁监控,任务监控,性能分析,系统诊断">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .nav {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #dee2e6;
        }

        .nav a {
            color: #495057;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav a:hover {
            background: #e9ecef;
        }

        .content {
            padding: 30px;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #6c757d;
        }

        .summary-card.warning {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }

        .summary-card.danger {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }

        .summary-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .tasks-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-header {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .duration {
            font-weight: bold;
        }

        .duration.normal {
            color: #28a745;
        }

        .duration.warning {
            color: #ffc107;
        }

        .duration.danger {
            color: #dc3545;
        }

        .thread-name {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ff6b6b;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state .icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn-group {
                justify-content: center;
            }
            
            .table {
                font-size: 0.9em;
            }
            
            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 死锁监控详情</h1>
            <p>实时监控长时间运行的任务和潜在的死锁情况</p>
        </div>
        
        <div class="nav">
            <a href="/">← 返回健康监控</a>
            <a href="/static/deadlock-monitor.html">死锁监控</a>
        </div>
        
        <div class="content">
            <div class="controls">
                <div class="btn-group">
                    <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
                    <button class="btn secondary" onclick="toggleAutoRefresh()">
                        ⏰ <span id="autoRefreshText">开启自动刷新</span>
                    </button>
                </div>
                <div class="btn-group">
                    <button class="btn" onclick="forceCleanup()">🧹 强制清理</button>
                </div>
            </div>

            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载监控数据...</p>
            </div>

            <div id="content" style="display: none;">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-number" id="totalTasks">0</div>
                        <div class="summary-label">总任务数</div>
                    </div>
                    <div class="summary-card" id="longRunningCard">
                        <div class="summary-number" id="longRunningTasks">0</div>
                        <div class="summary-label">长时间运行任务</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number" id="avgDuration">0s</div>
                        <div class="summary-label">平均运行时间</div>
                    </div>
                </div>

                <div class="tasks-table">
                    <div class="table-header">
                        📋 当前运行任务列表
                    </div>
                    <div id="tasksTableContainer">
                        <!-- 任务表格将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <div class="last-updated">
                <span id="lastUpdated">最后更新: 从未</span>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });

        // 刷新数据
        async function refreshData() {
            showLoading();
            
            try {
                const response = await fetch('/health/deadlock-report');
                const result = await response.json();
                
                if (result.code === 200) {
                    displayData(result.data);
                    updateLastUpdated();
                } else {
                    alert('获取数据失败: ' + result.msg);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 显示数据
        function displayData(data) {
            // 更新汇总卡片
            document.getElementById('totalTasks').textContent = data.total_tasks;
            document.getElementById('longRunningTasks').textContent = data.long_running_tasks;
            
            // 更新长时间运行任务卡片样式
            const longRunningCard = document.getElementById('longRunningCard');
            if (data.long_running_tasks > 0) {
                longRunningCard.className = 'summary-card danger';
            } else {
                longRunningCard.className = 'summary-card';
            }
            
            // 计算平均运行时间
            if (data.tasks.length > 0) {
                const avgDuration = data.tasks.reduce((sum, task) => sum + task.duration, 0) / data.tasks.length;
                document.getElementById('avgDuration').textContent = formatDuration(avgDuration);
            } else {
                document.getElementById('avgDuration').textContent = '0s';
            }
            
            // 显示任务表格
            displayTasksTable(data.tasks);
            
            document.getElementById('content').style.display = 'block';
        }

        // 显示任务表格
        function displayTasksTable(tasks) {
            const container = document.getElementById('tasksTableContainer');
            
            if (tasks.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">✅</div>
                        <h3>没有运行中的任务</h3>
                        <p>系统当前没有监控到任何运行中的任务</p>
                    </div>
                `;
                return;
            }
            
            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>任务ID</th>
                            <th>任务名称</th>
                            <th>运行时间</th>
                            <th>线程名称</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tasks.map(task => `
                            <tr>
                                <td><code>${task.task_id}</code></td>
                                <td>${task.task_name}</td>
                                <td class="duration ${getDurationClass(task.duration)}">${formatDuration(task.duration)}</td>
                                <td><span class="thread-name">${task.thread_name}</span></td>
                                <td>${task.is_long_running ? '<span style="color: #dc3545;">⚠️ 长时间运行</span>' : '<span style="color: #28a745;">✅ 正常</span>'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        // 格式化持续时间
        function formatDuration(seconds) {
            if (seconds < 60) {
                return Math.round(seconds) + 's';
            } else if (seconds < 3600) {
                return Math.round(seconds / 60) + 'm ' + Math.round(seconds % 60) + 's';
            } else {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return hours + 'h ' + minutes + 'm';
            }
        }

        // 获取持续时间样式类
        function getDurationClass(seconds) {
            if (seconds < 60) return 'normal';
            if (seconds < 300) return 'warning';
            return 'danger';
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                document.getElementById('autoRefreshText').textContent = '开启自动刷新';
            } else {
                autoRefreshInterval = setInterval(refreshData, 10000); // 10秒刷新一次
                isAutoRefresh = true;
                document.getElementById('autoRefreshText').textContent = '关闭自动刷新';
            }
        }

        // 强制清理
        async function forceCleanup() {
            if (!confirm('确定要执行强制清理吗？这将清理所有长时间运行的任务。')) {
                return;
            }
            
            try {
                const response = await fetch('/health/force-cleanup', { method: 'POST' });
                const result = await response.json();
                
                if (result.code === 200) {
                    alert('清理完成！清理了 ' + result.data.long_running_tasks_cleared + ' 个长时间运行的任务。');
                    refreshData();
                } else {
                    alert('清理失败: ' + result.msg);
                }
            } catch (error) {
                alert('清理失败: ' + error.message);
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 更新最后更新时间
        function updateLastUpdated() {
            document.getElementById('lastUpdated').textContent = '最后更新: ' + new Date().toLocaleString();
        }
    </script>
</body>
</html>
