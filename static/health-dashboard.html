<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统健康监控面板</title>
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <meta name="description" content="实时监控系统组件状态和性能指标">
    <meta name="keywords" content="健康监控,系统状态,性能监控,死锁检测">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #6c757d;
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-card.healthy {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }

        .status-card.warning {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }

        .status-card.error {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }

        .status-card.degraded {
            border-left-color: #fd7e14;
            background: linear-gradient(135deg, #ffecd1 0%, #fcb69f 100%);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-badge.healthy {
            background: #28a745;
            color: white;
        }

        .status-badge.warning {
            background: #ffc107;
            color: #212529;
        }

        .status-badge.error {
            background: #dc3545;
            color: white;
        }

        .status-badge.degraded {
            background: #fd7e14;
            color: white;
        }

        .card-content {
            color: #555;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
        }

        .metric-value {
            font-weight: bold;
        }

        .controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 系统健康监控面板</h1>
            <p>实时监控系统组件状态和性能指标</p>
        </div>
        
        <div class="content">
            <div class="controls">
                <button class="btn" onclick="refreshStatus()">🔄 刷新状态</button>
                <button class="btn" onclick="toggleAutoRefresh()">⏰ <span id="autoRefreshText">开启自动刷新</span></button>
                <button class="btn" onclick="window.open('/static/deadlock-monitor.html', '_blank')">🔍 死锁监控详情</button>
                <button class="btn danger" onclick="forceCleanup()">🧹 强制清理</button>
            </div>

            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载系统状态...</p>
            </div>

            <div id="error" class="error-message" style="display: none;"></div>

            <div id="statusGrid" class="status-grid" style="display: none;"></div>

            <div class="last-updated">
                <span id="lastUpdated">最后更新: 从未</span>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
        });

        // 刷新状态
        async function refreshStatus() {
            showLoading();
            hideError();
            
            try {
                const response = await fetch('/health/status');
                const result = await response.json();
                
                if (result.code === 200) {
                    displayStatus(result.data);
                    updateLastUpdated();
                } else {
                    showError('获取状态失败: ' + result.msg);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 显示状态
        function displayStatus(data) {
            const grid = document.getElementById('statusGrid');
            grid.innerHTML = '';

            // 系统总体状态
            const overallCard = createStatusCard('系统总体状态', data.status, {
                '状态': data.status,
                '检查时间': new Date(data.timestamp * 1000).toLocaleString()
            });
            grid.appendChild(overallCard);

            // 各组件状态
            for (const [componentName, componentData] of Object.entries(data.components)) {
                const card = createComponentCard(componentName, componentData);
                grid.appendChild(card);
            }

            grid.style.display = 'grid';
        }

        // 创建状态卡片
        function createStatusCard(title, status, metrics) {
            const card = document.createElement('div');
            card.className = `status-card ${status}`;
            
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">${title}</div>
                    <div class="status-badge ${status}">${getStatusText(status)}</div>
                </div>
                <div class="card-content">
                    ${Object.entries(metrics).map(([key, value]) => 
                        `<div class="metric">
                            <span class="metric-label">${key}:</span>
                            <span class="metric-value">${value}</span>
                        </div>`
                    ).join('')}
                </div>
            `;
            
            return card;
        }

        // 创建组件卡片
        function createComponentCard(componentName, componentData) {
            const title = getComponentTitle(componentName);
            const status = componentData.status;
            
            let metrics = {};
            
            if (componentName === 'thread_pools') {
                metrics['总体状态'] = status;
                for (const [poolName, poolData] of Object.entries(componentData.pools || {})) {
                    metrics[`${poolName.toUpperCase()}线程池`] = `${poolData.active_threads}/${poolData.max_workers} (队列:${poolData.queue_size})`;
                }
            } else if (componentName === 'deadlock_monitor') {
                metrics['监控状态'] = componentData.is_monitoring ? '运行中' : '已停止';
                metrics['总任务数'] = componentData.total_tasks;
                metrics['长时间运行任务'] = componentData.long_running_tasks;
                if (componentData.warning) {
                    metrics['警告'] = componentData.warning;
                }
            } else if (componentName === 'event_loop') {
                metrics['运行状态'] = componentData.is_running ? '运行中' : '已停止';
                metrics['是否关闭'] = componentData.is_closed ? '是' : '否';
                if (componentData.error) {
                    metrics['错误'] = componentData.error;
                }
            }
            
            return createStatusCard(title, status, metrics);
        }

        // 获取组件标题
        function getComponentTitle(componentName) {
            const titles = {
                'thread_pools': '线程池状态',
                'deadlock_monitor': '死锁监控',
                'event_loop': '事件循环'
            };
            return titles[componentName] || componentName;
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'healthy': '健康',
                'warning': '警告',
                'error': '错误',
                'degraded': '降级',
                'disabled': '禁用'
            };
            return texts[status] || status;
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                document.getElementById('autoRefreshText').textContent = '开启自动刷新';
            } else {
                autoRefreshInterval = setInterval(refreshStatus, 30000); // 30秒刷新一次
                isAutoRefresh = true;
                document.getElementById('autoRefreshText').textContent = '关闭自动刷新';
            }
        }

        // 强制清理
        async function forceCleanup() {
            if (!confirm('确定要执行强制清理吗？这将清理所有长时间运行的任务。')) {
                return;
            }
            
            try {
                const response = await fetch('/health/force-cleanup', { method: 'POST' });
                const result = await response.json();
                
                if (result.code === 200) {
                    alert('清理完成！清理了 ' + result.data.long_running_tasks_cleared + ' 个长时间运行的任务。');
                    refreshStatus();
                } else {
                    alert('清理失败: ' + result.msg);
                }
            } catch (error) {
                alert('清理失败: ' + error.message);
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('statusGrid').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 显示错误
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 隐藏错误
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        // 更新最后更新时间
        function updateLastUpdated() {
            document.getElementById('lastUpdated').textContent = '最后更新: ' + new Date().toLocaleString();
        }
    </script>
</body>
</html>
