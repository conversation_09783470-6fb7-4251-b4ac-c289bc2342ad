# 健康监控前端页面使用说明

## 📋 页面概览

本项目提供了两个前端监控页面，用于实时监控系统健康状态和死锁情况：

### 1. 主健康监控面板 (`health-dashboard.html`)
- **访问地址**: `http://localhost:8000/` 或 `http://localhost:8000/static/health-dashboard.html`
- **功能**: 显示系统整体健康状态，包括线程池、死锁监控、事件循环等组件状态

### 2. 死锁监控详情页 (`deadlock-monitor.html`)
- **访问地址**: `http://localhost:8000/static/deadlock-monitor.html`
- **功能**: 详细显示当前运行的任务列表，监控长时间运行的任务

## 🎯 主要功能

### 健康监控面板功能
- ✅ **实时状态显示**: 显示系统各组件的健康状态
- 🔄 **自动刷新**: 可开启30秒自动刷新
- 📊 **状态分类**: 健康、警告、错误、降级等状态一目了然
- 🧹 **强制清理**: 紧急情况下清理长时间运行的任务
- 📱 **响应式设计**: 支持手机和平板访问

### 死锁监控详情功能
- 📋 **任务列表**: 显示所有当前运行的任务
- ⏱️ **运行时间**: 实时显示每个任务的运行时长
- 🚨 **长时间任务告警**: 自动标识可能卡死的任务
- 📈 **统计信息**: 显示总任务数、长时间运行任务数等
- 🔄 **快速刷新**: 10秒自动刷新，更及时发现问题

## 🎨 状态颜色说明

### 健康状态颜色
- 🟢 **绿色 (Healthy)**: 系统运行正常
- 🟡 **黄色 (Warning)**: 有警告但不影响正常运行
- 🟠 **橙色 (Degraded)**: 性能降级，需要关注
- 🔴 **红色 (Error)**: 出现错误，需要立即处理

### 任务运行时间颜色
- 🟢 **绿色**: 运行时间 < 1分钟（正常）
- 🟡 **黄色**: 运行时间 1-5分钟（需要关注）
- 🔴 **红色**: 运行时间 > 5分钟（可能卡死）

## 🔧 操作指南

### 基本操作
1. **刷新状态**: 点击"🔄 刷新状态"按钮手动更新数据
2. **自动刷新**: 点击"⏰ 开启自动刷新"启用定时刷新
3. **查看详情**: 点击"🔍 死锁监控详情"查看任务详情
4. **强制清理**: 紧急情况下点击"🧹 强制清理"清理卡死任务

### 监控建议
- **日常监控**: 每天查看1-2次健康状态
- **高峰期监控**: 业务高峰期开启自动刷新
- **问题排查**: 发现问题时查看死锁监控详情
- **紧急处理**: 系统卡死时使用强制清理功能

## 📊 监控指标说明

### 线程池状态
- **活跃线程数/最大线程数**: 显示当前使用情况
- **队列长度**: 等待执行的任务数量
- **状态**: healthy(健康) / busy(繁忙) / overloaded(过载)

### 死锁监控
- **监控状态**: 监控器是否正常运行
- **总任务数**: 当前监控的任务总数
- **长时间运行任务**: 可能卡死的任务数量

### 事件循环
- **运行状态**: 事件循环是否正常运行
- **是否关闭**: 检查事件循环状态

## 🚨 告警处理

### 常见告警及处理方法

#### 1. 线程池过载 (Overloaded)
**现象**: 队列长度 > 最大线程数 * 2
**处理**: 
- 检查是否有大量并发请求
- 考虑增加线程池大小
- 优化任务执行效率

#### 2. 长时间运行任务
**现象**: 任务运行时间 > 5分钟
**处理**:
- 查看任务详情确认是否卡死
- 检查相关日志
- 必要时执行强制清理

#### 3. 死锁监控停止
**现象**: 监控状态显示"已停止"
**处理**:
- 重启服务
- 检查系统资源
- 查看错误日志

## 📱 移动端使用

页面采用响应式设计，支持手机和平板访问：
- 自动适配屏幕尺寸
- 触摸友好的按钮设计
- 简化的移动端布局

## 🔗 API 接口

前端页面调用以下API接口：
- `GET /health/status` - 获取系统健康状态
- `GET /health/deadlock-report` - 获取死锁监控报告
- `GET /health/thread-pools` - 获取线程池状态
- `POST /health/force-cleanup` - 强制清理资源

## 🛠️ 自定义配置

如需自定义监控页面，可以修改以下文件：
- `static/health-dashboard.html` - 主监控页面
- `static/deadlock-monitor.html` - 死锁监控页面

### 可调整的参数
- 自动刷新间隔（默认30秒/10秒）
- 颜色主题
- 告警阈值
- 显示字段

## 📞 技术支持

如遇到问题，请：
1. 检查服务器是否正常运行
2. 确认网络连接正常
3. 查看浏览器控制台错误信息
4. 检查服务器日志
