/* 通用样式文件 */

/* 状态颜色定义 */
:root {
    --color-healthy: #28a745;
    --color-warning: #ffc107;
    --color-error: #dc3545;
    --color-degraded: #fd7e14;
    --color-disabled: #6c757d;
    
    --bg-healthy: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    --bg-warning: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    --bg-error: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    --bg-degraded: linear-gradient(135deg, #ffecd1 0%, #fcb69f 100%);
    
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 15px rgba(0,0,0,0.2);
    --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
    
    --border-radius: 10px;
    --border-radius-large: 15px;
    --border-radius-small: 5px;
}

/* 通用重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 通用字体 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 通用按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:active {
    transform: translateY(0);
}

.btn.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.btn.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.btn.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.btn.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.btn.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* 状态徽章 */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.healthy {
    background: var(--color-healthy);
    color: white;
}

.status-badge.warning {
    background: var(--color-warning);
    color: #212529;
}

.status-badge.error {
    background: var(--color-error);
    color: white;
}

.status-badge.degraded {
    background: var(--color-degraded);
    color: white;
}

.status-badge.disabled {
    background: var(--color-disabled);
    color: white;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 0.9em;
    color: #6c757d;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tr:hover {
    background: #f8f9fa;
}

.table tr:last-child td {
    border-bottom: none;
}

/* 加载动画 */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-muted { color: #6c757d; }
.text-primary { color: #007bff; }
.text-success { color: var(--color-healthy); }
.text-warning { color: var(--color-warning); }
.text-danger { color: var(--color-error); }

.bg-light { background-color: #f8f9fa; }
.bg-white { background-color: white; }

.border { border: 1px solid #dee2e6; }
.border-top { border-top: 1px solid #dee2e6; }
.border-bottom { border-bottom: 1px solid #dee2e6; }
.border-left { border-left: 1px solid #dee2e6; }
.border-right { border-right: 1px solid #dee2e6; }

.rounded { border-radius: var(--border-radius-small); }
.rounded-lg { border-radius: var(--border-radius); }
.rounded-xl { border-radius: var(--border-radius-large); }

.shadow-sm { box-shadow: var(--shadow-light); }
.shadow { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-heavy); }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 1rem; }
.m-4 { margin: 1.5rem; }
.m-5 { margin: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

/* 响应式工具类 */
@media (max-width: 768px) {
    .d-md-none { display: none; }
    .d-md-block { display: block; }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.8em;
    }
    
    .table {
        font-size: 0.9em;
    }
    
    .table th,
    .table td {
        padding: 8px 10px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 提示框样式 */
.alert {
    padding: 15px;
    border-radius: var(--border-radius-small);
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: var(--bg-healthy);
    border-color: var(--color-healthy);
    color: #155724;
}

.alert-warning {
    background: var(--bg-warning);
    border-color: var(--color-warning);
    color: #856404;
}

.alert-danger {
    background: var(--bg-error);
    border-color: var(--color-error);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-color: #17a2b8;
    color: #0c5460;
}
