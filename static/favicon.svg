<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="healthGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="50" cy="50" r="45" fill="url(#healthGradient)" stroke="#fff" stroke-width="2"/>
  
  <!-- 心跳线 -->
  <path d="M15 50 L25 50 L30 35 L35 65 L40 30 L45 70 L50 40 L55 60 L60 45 L65 55 L70 50 L85 50" 
        stroke="#fff" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- 心形图标 -->
  <path d="M50 75 C45 70, 35 60, 35 50 C35 45, 40 40, 45 40 C47 40, 49 41, 50 43 C51 41, 53 40, 55 40 C60 40, 65 45, 65 50 C65 60, 55 70, 50 75 Z" 
        fill="#fff" opacity="0.9"/>
</svg>
