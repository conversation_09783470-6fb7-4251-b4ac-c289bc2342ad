{"flag": "release", "explain": "正式环境配置", "version": "1.0.0.0529", "log_dir": "/qyd_agents/logs", "cross_domain": false, "search_result_filtering": false, "request": {"verify": false}, "llm": {"chat-use": "deepseek-r1: 7b", "generate-use": "qwen2.5:7b", "llm_list": [{"model": "qwen2.5:7b", "engine": "ollama", "base_url": "http://#localhost#:11434", "model_think": "no_think"}, {"model": "qwen3:8b", "engine": "ollama", "base_url": "http://#localhost#:11434", "model_think": "all"}, {"model": "deepseek-r1:7b", "engine": "ollama", "base_url": "http://#localhost#:11434", "model_think": "think"}]}, "embedding": {"name": "ollama", "ollama": {"path": "http://#localhost#:11434", "model": "bge-m3", "max_tokens": 2000}, "lmstudio": {"path": "http://#localhost#:1444/v1", "model": "text-embedding-bge-m3"}}, "vector_db_type": "mil<PERSON><PERSON>", "retrievers": {"ensemble": {"k": 5, "score_threshold": 0.7, "fetch_k": 10, "bm25_weight": 0.3}, "vector": {"k": 3, "score_threshold": 0.7, "fetch_k": 10}}, "agents": {"memory_path": "/qyd_agents/memos", "kb_agent": {"temperature": 0.3, "verb": true, "max_token_limit": 1500}, "expert_agent": {"temperature": 0.3, "verb": true, "max_token_limit": 1500}, "question_agent": {"temperature": 0.3, "verb": true, "max_token_limit": 3000, "mention_percent": 0.25, "min_mention_chars": 2500}}, "faiss": {"save_path": "/qyd_agents/faiss"}, "nacos": {"enabled": true, "server_addresses": "http://#localhost#:8848", "namespace": "3d1f4237-c4ba-4be9-80f7-ad23d3670a83", "group": "DEFAULT_GROUP", "username": "nacos", "password": "nacos", "service": {"service_name": "agent-service", "ip": "#localhost#", "port": 8000, "cluster_name": "DEFAULT", "group_name": "DEFAULT_GROUP", "metadata": {"preserved.register.source": "PYTHON"}}}, "mysql": {"database": "ai_kb_box", "user": "root", "password": "admin1234", "host": "#localhost#", "port": 3306, "connectionLimit": 10}, "vector_gen": {"enabled": true, "cache_dir": "/qyd_agents/vector_gen_cache", "temp_dir": "/qyd_agents/vector_gen_temp", "concurrency": 2}, "sqlite": {"enabled": true, "database": "/qyd_agents/sqliteDB", "db_name": "default_sqlite"}, "milvus": {"host": "#localhost#", "port": "19530", "user": "root", "password": "mil<PERSON><PERSON>", "db_name": "default", "collection_name": "collection_agent_service"}, "minio": {"endpoint": "#localhost#:9000", "access_key": "minioadmin", "secret_key": "minioadmin", "bucket": "bucket01", "secure": false}, "pipeline": {"enabled": true, "cache_dir": "/qyd_agents/pipeline"}, "talent": {"talent_url": "http://#localhost#:8080", "file_dir": "/qyd_agents/talent", "tokens": 6144, "match_score": 60.0}, "server": {"workers": 2, "limit_concurrency": 100, "limit_max_requests": 1000}, "async_config": {"max_llm_workers": 5, "max_file_workers": 3, "max_download_workers": 5}}