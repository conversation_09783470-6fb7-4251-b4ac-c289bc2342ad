import asyncio
import threading
import time
import traceback
from typing import Dict, List, Optional
from dataclasses import dataclass
from Utils.logs.LoggingConfig import logger


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    task_name: str
    start_time: float
    thread_name: str
    stack_trace: str
    timeout_threshold: float = 300.0  # 5分钟默认超时


class DeadlockMonitor:
    """死锁和卡死监控器"""
    
    def __init__(self, check_interval: float = 30.0):
        self.check_interval = check_interval
        self.running_tasks: Dict[str, TaskInfo] = {}
        self.lock = threading.Lock()
        self.monitor_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        
    def register_task(self, task_id: str, task_name: str, timeout_threshold: float = 300.0):
        """注册一个需要监控的任务"""
        with self.lock:
            current_time = time.time()
            thread_name = threading.current_thread().name
            stack_trace = ''.join(traceback.format_stack())
            
            task_info = TaskInfo(
                task_id=task_id,
                task_name=task_name,
                start_time=current_time,
                thread_name=thread_name,
                stack_trace=stack_trace,
                timeout_threshold=timeout_threshold
            )
            
            self.running_tasks[task_id] = task_info
            logger.debug(f"Registered task {task_id} ({task_name}) for monitoring")
    
    def unregister_task(self, task_id: str):
        """取消注册任务"""
        with self.lock:
            if task_id in self.running_tasks:
                task_info = self.running_tasks.pop(task_id)
                duration = time.time() - task_info.start_time
                logger.debug(f"Unregistered task {task_id} ({task_info.task_name}) after {duration:.2f}s")
    
    def get_long_running_tasks(self) -> List[TaskInfo]:
        """获取长时间运行的任务"""
        current_time = time.time()
        long_running = []
        
        with self.lock:
            for task_info in self.running_tasks.values():
                duration = current_time - task_info.start_time
                if duration > task_info.timeout_threshold:
                    long_running.append(task_info)
        
        return long_running
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                long_running_tasks = self.get_long_running_tasks()
                
                if long_running_tasks:
                    logger.warning(f"Found {len(long_running_tasks)} potentially stuck tasks:")
                    for task_info in long_running_tasks:
                        duration = time.time() - task_info.start_time
                        logger.warning(
                            f"Task {task_info.task_id} ({task_info.task_name}) "
                            f"running for {duration:.2f}s in thread {task_info.thread_name}"
                        )
                        logger.debug(f"Stack trace for task {task_info.task_id}:\n{task_info.stack_trace}")
                
                # 检查线程池状态
                await self._check_thread_pools()
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in deadlock monitor: {str(e)}")
                await asyncio.sleep(self.check_interval)
    
    async def _check_thread_pools(self):
        """检查线程池状态"""
        try:
            from Utils.AsyncConfig import async_config
            
            # 检查各个线程池的状态
            executors = [
                ("LLM", async_config._llm_executor),
                ("File", async_config._file_executor),
                ("Download", async_config._download_executor)
            ]
            
            for name, executor in executors:
                if executor is not None:
                    # 获取线程池统计信息
                    active_count = getattr(executor, '_threads', set())
                    if hasattr(executor, '_work_queue'):
                        queue_size = executor._work_queue.qsize()
                        logger.debug(f"{name} executor: {len(active_count)} active threads, {queue_size} queued tasks")
                        
                        # 如果队列积压过多，发出警告
                        if queue_size > 10:
                            logger.warning(f"{name} executor has {queue_size} queued tasks - possible bottleneck")
                    
        except Exception as e:
            logger.debug(f"Error checking thread pools: {str(e)}")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            try:
                loop = asyncio.get_running_loop()
                self.monitor_task = loop.create_task(self._monitor_loop())
                logger.info("Deadlock monitor started")
            except RuntimeError:
                logger.warning("No running event loop, deadlock monitor not started")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_task and not self.monitor_task.done():
            self.monitor_task.cancel()
            logger.info("Deadlock monitor stopped")
    
    def get_status_report(self) -> Dict:
        """获取状态报告"""
        current_time = time.time()
        report = {
            "total_tasks": len(self.running_tasks),
            "long_running_tasks": len(self.get_long_running_tasks()),
            "tasks": []
        }
        
        with self.lock:
            for task_info in self.running_tasks.values():
                duration = current_time - task_info.start_time
                report["tasks"].append({
                    "task_id": task_info.task_id,
                    "task_name": task_info.task_name,
                    "duration": duration,
                    "thread_name": task_info.thread_name,
                    "is_long_running": duration > task_info.timeout_threshold
                })
        
        return report


# 全局监控器实例
deadlock_monitor = DeadlockMonitor()


class TaskMonitorContext:
    """任务监控上下文管理器"""
    
    def __init__(self, task_name: str, timeout_threshold: float = 300.0):
        self.task_name = task_name
        self.timeout_threshold = timeout_threshold
        self.task_id = f"{task_name}_{int(time.time() * 1000)}"
    
    def __enter__(self):
        deadlock_monitor.register_task(self.task_id, self.task_name, self.timeout_threshold)
        return self.task_id
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        deadlock_monitor.unregister_task(self.task_id)


async def monitor_async_task(task_name: str, timeout_threshold: float = 300.0):
    """异步任务监控装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            with TaskMonitorContext(f"{func.__name__}_{task_name}", timeout_threshold):
                return await func(*args, **kwargs)
        return wrapper
    return decorator


def monitor_sync_task(task_name: str, timeout_threshold: float = 300.0):
    """同步任务监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with TaskMonitorContext(f"{func.__name__}_{task_name}", timeout_threshold):
                return func(*args, **kwargs)
        return wrapper
    return decorator
