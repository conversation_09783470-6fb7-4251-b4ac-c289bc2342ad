# 前端健康监控页面部署指南

## 🚀 快速开始

### 1. 确保服务器运行
```bash
# 启动服务器
python Server.py
```

### 2. 访问监控页面
- **主健康监控面板**: http://localhost:8000/
- **死锁监控详情**: http://localhost:8000/static/deadlock-monitor.html

## 📁 文件结构

```
static/
├── health-dashboard.html      # 主健康监控面板
├── deadlock-monitor.html      # 死锁监控详情页
├── common.css                 # 通用样式文件
├── favicon.svg                # 网站图标
└── README.md                  # 使用说明
```

## 🎯 页面功能

### 主健康监控面板
- ✅ 系统整体健康状态展示
- 📊 线程池状态监控
- 🔍 死锁监控状态
- ⚡ 事件循环状态检查
- 🔄 自动刷新功能（30秒间隔）
- 🧹 强制清理功能

### 死锁监控详情页
- 📋 当前运行任务列表
- ⏱️ 任务运行时间统计
- 🚨 长时间运行任务告警
- 📈 任务统计信息
- 🔄 快速刷新（10秒间隔）

## 🎨 界面特性

### 响应式设计
- 📱 支持手机、平板、桌面访问
- 🎯 自适应布局
- 👆 触摸友好的交互

### 视觉效果
- 🌈 渐变色彩设计
- 💫 平滑动画效果
- 🎨 状态颜色区分
- 📊 直观的数据展示

### 用户体验
- ⚡ 快速加载
- 🔄 实时更新
- 🎯 一键操作
- 📱 移动端优化

## 🔧 自定义配置

### 修改刷新间隔
在HTML文件中找到以下代码并修改：
```javascript
// 主监控页面 - 30秒刷新
autoRefreshInterval = setInterval(refreshStatus, 30000);

// 死锁监控页面 - 10秒刷新
autoRefreshInterval = setInterval(refreshData, 10000);
```

### 修改颜色主题
编辑 `common.css` 文件中的CSS变量：
```css
:root {
    --color-healthy: #28a745;    /* 健康状态颜色 */
    --color-warning: #ffc107;    /* 警告状态颜色 */
    --color-error: #dc3545;      /* 错误状态颜色 */
    --color-degraded: #fd7e14;   /* 降级状态颜色 */
}
```

### 添加新的监控指标
1. 在后端API中添加新的数据字段
2. 在前端JavaScript中处理新数据
3. 在HTML中添加显示元素

## 🔌 API接口

前端页面调用以下后端接口：

### 健康状态接口
```
GET /health/status
返回: 系统整体健康状态
```

### 死锁监控接口
```
GET /health/deadlock-report
返回: 当前运行任务详情
```

### 线程池状态接口
```
GET /health/thread-pools
返回: 线程池详细状态
```

### 强制清理接口
```
POST /health/force-cleanup
功能: 清理长时间运行的任务
```

## 🛠️ 开发指南

### 本地开发
1. 修改HTML/CSS/JS文件
2. 刷新浏览器查看效果
3. 无需重启服务器

### 添加新页面
1. 在 `static/` 目录创建新的HTML文件
2. 在 `Server.py` 中添加路由（如需要）
3. 更新导航链接

### 调试技巧
- 使用浏览器开发者工具
- 检查网络请求状态
- 查看控制台错误信息
- 使用断点调试JavaScript

## 🔒 安全考虑

### 生产环境部署
- 考虑添加身份验证
- 限制访问IP范围
- 使用HTTPS协议
- 定期更新依赖

### 权限控制
- 强制清理功能需要管理员权限
- 敏感信息脱敏显示
- 操作日志记录

## 📊 性能优化

### 前端优化
- 压缩CSS/JS文件
- 使用CDN加速
- 启用浏览器缓存
- 图片优化

### 后端优化
- API响应缓存
- 数据库查询优化
- 异步处理
- 负载均衡

## 🐛 故障排查

### 常见问题

#### 页面无法访问
1. 检查服务器是否启动
2. 确认端口8000未被占用
3. 检查防火墙设置

#### 数据不更新
1. 检查API接口是否正常
2. 查看浏览器控制台错误
3. 确认网络连接正常

#### 样式显示异常
1. 清除浏览器缓存
2. 检查CSS文件是否加载
3. 确认文件路径正确

### 日志查看
```bash
# 查看服务器日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 📈 监控建议

### 日常使用
- 每天查看1-2次健康状态
- 设置浏览器书签便于访问
- 关注长时间运行任务

### 告警设置
- 配置邮件/短信告警
- 设置监控阈值
- 建立应急响应流程

### 数据分析
- 定期导出监控数据
- 分析性能趋势
- 优化系统配置

## 🔄 版本更新

### 更新流程
1. 备份现有文件
2. 下载新版本文件
3. 更新配置文件
4. 测试功能正常
5. 部署到生产环境

### 兼容性
- 向后兼容API接口
- 保持配置文件格式
- 提供迁移指南

## 📞 技术支持

如需帮助，请：
1. 查看本文档
2. 检查GitHub Issues
3. 联系技术团队
4. 提交Bug报告

---

**注意**: 本监控系统仅用于开发和测试环境，生产环境使用请添加适当的安全措施。
